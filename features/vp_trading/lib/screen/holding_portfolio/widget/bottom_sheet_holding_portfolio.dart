import 'package:flutter/material.dart';
import 'package:vp_common/extensions/context_extensions.dart';
import 'package:vp_common/utils/format_utils.dart';
import 'package:vp_common/utils/money_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/divider_widget.dart';
import 'package:vp_design_system/widget/button/vps_button.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/holding_portfolio/holding_portfolio_stock_model.dart';
import 'package:vp_trading/router/trading_router.dart';

class BottomSheetHoldingPortfolio extends StatelessWidget {
  const BottomSheetHoldingPortfolio({
    super.key,
    required this.item,
    required this.marketValueCategory,
    this.subAccountFilter,
  });

  final HoldingPortfolioStockModel item;
  final num marketValueCategory;
  final SubAccountModel? subAccountFilter;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: vpColor.backgroundElevation0,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        spacing: 8,
        children: [
          _buildInfoRow(
            context,
            VPTradingLocalize.current.trading_stock_symbol,
            item.symbol ?? '-',
            isBold: true,
          ),
          _buildInfoRow(
            context,
            VPTradingLocalize.current.trading_total_stock_volume,
            MoneyUtils.formatMoney((item.total ?? 0).toDouble(), suffix: ''),
          ),
          _buildInfoRow(
            context,
            VPTradingLocalize.current.trading_average_cost_price,
            item.formatPriceView ?? "-",
          ),
          _buildInfoRow(
            context,
            VPTradingLocalize.current.trading_investment_principal,
            MoneyUtils.formatMoney((item.costPriceValue).toDouble()),
          ),
          _buildInfoRow(
            context,
            VPTradingLocalize.current.trading_market_value,
            MoneyUtils.formatMoney((item.marketValue).toDouble()),
          ),
          _buildProfitRow(context),

          // Divider
          const SizedBox(height: 8),
          const DividerWidget(),
          const SizedBox(height: 8),

          // Market Info Section
          _buildInfoRow(
            context,
            VPTradingLocalize.current.trading_available_stock_volume,
            "${item.trade ?? 0}",
            isBold: true,
          ),
          _buildInfoRow(
            context,
            VPTradingLocalize.current.trading_pending_stock,
            _formatTStatus(
              item.receivingT0,
              item.receivingT1,
              item.receivingT2,
            ),
          ),
          _buildInfoRow(
            context,
            VPTradingLocalize.current.trading_pending_rights,
            MoneyUtils.formatMoney(
              (item.receivingRight ?? 0).toDouble(),
              suffix: '',
            ),
          ),
          _buildInfoRow(
            context,
            VPTradingLocalize.current.trading_restricted_stock,
            "${item.blocked ?? 0}",
          ),
             _buildInfoRow(
            context,
            VPTradingLocalize.current.trading_pending_rights,
            MoneyUtils.formatMoney(
              (item.receivingRight ?? 0).toDouble(),
              suffix: '',
            ),
          ),
          _buildInfoRow(
            context,
            VPTradingLocalize.current.trading_restricted_stock,
            "${item.blocked ?? 0}",
          ),
          _buildInfoRow(
            context,
            VPTradingLocalize.current.trading_portfolio_weight,
            FormatUtils.formatPercent(
                  (item.marketValue / marketValueCategory) * 100,
                  showSign: false,
                ) ??
                "-",
          ),

          const SizedBox(height: 8),
          const DividerWidget(),
          const SizedBox(height: 8),
          // Action Bar
          Row(
            children: [
              Expanded(
                child: VpsButton.primarySmall(
                  title: VPTradingLocalize.current.trading_buy,
                  onPressed: () {
                    _navigateToOrder(context, true);
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: VpsButton.primaryDangerSmall(
                  title: VPTradingLocalize.current.trading_sell,
                  onPressed: () {
                    _navigateToOrder(context, false);
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value, {
    bool isBold = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style:
                  isBold
                      ? vpTextStyle.captionMedium?.copyWith(
                        color: vpColor.textSecondary,
                      )
                      : vpTextStyle.captionRegular?.copyWith(
                        color: vpColor.textTertiary,
                      ),
            ),
          ),
          Text(
            value,
            style: context.textTheme.bodySmall?.copyWith(
              color: vpColor.textPrimary,
              fontWeight: isBold ? FontWeight.w500 : FontWeight.w600,
              fontSize: 12,
              height: 1.33,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfitRow(BuildContext context) {
    if (item.pnlAmt == null || item.pnlRate == null) {
      return _buildInfoRow(
        context,
        VPTradingLocalize.current.trading_expected_profit_loss,
        '-',
        isBold: true,
      );
    }
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            child: Text(
              VPTradingLocalize.current.trading_expected_profit_loss,
              style: context.textTheme.bodySmall?.copyWith(
                color: vpColor.textSecondary,
                fontWeight: FontWeight.w500,
                fontSize: 12,
                height: 1.33,
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "${item.pnlRateDirectionSymbol} ${item.pnlView ?? '-'}",
                style: context.textStyle.subtitle14?.copyWith(
                  color: item.colorPnl,
                ),
              ),
              Text(
                "(${item.pnlRateView ?? '-'})",
                style: vpTextStyle.body14?.copyWith(color: item.colorPnl),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatTStatus(num? t0, num? t1, num? t2) {
    return 'T0: ${MoneyUtils.formatMoney((t0 ?? 0).toDouble(), suffix: '')} T1: ${MoneyUtils.formatMoney((t1 ?? 0).toDouble(), suffix: '')} T2: ${MoneyUtils.formatMoney((t2 ?? 0).toDouble(), suffix: '')}';
  }

  _navigateToOrder(BuildContext context, bool isBuy) {

    // Loại tiểu khoản
    // Nếu đang chọn loại tiểu khoản → Auto fill theo loại tiểu khoản mà user đang lựa chọn
    // Nếu chọn tất cả tiểu khoản:
    // Nếu mã đó chỉ nằm ở 1 danh mục → Auto fill tiểu khoản đang nắm giữ mã đó
    // Nếu mã đó nằm ở >1 danh mục → Auto fill tiểu khoản mặc định của khách hàng
    final SubAccountType subAccountTypePofolio =
        (subAccountFilter != null &&
                subAccountFilter!.accountType != SubAccountType.all)
            ? subAccountFilter!.accountType
            : (item.isSymbolInBothSubAccounts ?? false)
            ? GetIt.instance<SubAccountCubit>().defaultSubAccount.accountType
            : SubAccountType.fromCodeToEnum(item.productTypeCd ?? '') ??
                SubAccountType.normal;
    context.pop();
    context.pushNamed(
      TradingRouter.placeOrder.routeName,
      queryParameters:
          PlaceOrderArgs(
            subAccountType: subAccountTypePofolio,
            symbol: item.symbol ?? "VPB",
            action: isBuy ? OrderAction.buy : OrderAction.sell,
          ).toQueryParams(),
    );
  }
}

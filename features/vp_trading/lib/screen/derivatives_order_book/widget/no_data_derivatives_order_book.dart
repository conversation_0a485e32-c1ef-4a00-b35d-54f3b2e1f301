import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/generated/l10n.dart';

class NoDataDerivativesOrderBook extends StatelessWidget {
  const NoDataDerivativesOrderBook({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox.expand(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          VpTradingAssets.icons.icNoneData.svg(),
          Text(
            VPTradingLocalize.current.trading_no_data_message,
            style: context.textStyle.body14?.copyWith(
              color: vpColor.textPrimary,
            ),
          ),
          const SizedBox(height: 24),
          VpsButton.primarySmall(title: "Đặt lệnh", onPressed: () {}),
          const SizedBox(height: 56),
        ],
      ),
    );
  }
}

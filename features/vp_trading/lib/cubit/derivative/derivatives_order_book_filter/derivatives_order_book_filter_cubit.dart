import 'package:equatable/equatable.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/model/request/derivatives_order_book/derivatives_filter_param.dart';
import 'package:vp_trading/model/request/order/order_book_request.dart';
import 'package:vp_trading/screen/order_container/enum/order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';

part 'derivatives_order_book_filter_state.dart';

/// Cubit for managing derivatives order book filter state
/// Implements dual cubit pattern following VP Utility Module standards
/// Maintains independent filter state for Regular Orders and Conditional Orders tabs
class DerivativesOrderBookFilterCubit
    extends Cubit<DerivativesOrderBookFilterState> {
  DerivativesOrderBookFilterCubit()
    : super(const DerivativesOrderBookFilterState());

  /// Initialize with default filter values
  void init() {
    const defaultFilter = DerivativesFilterParam(
      transactionType: OrderTypeEnum.all,
      orderStatus: [OrderStatusEnum.all],
    );

    // Get current account info
    final subAccountCubit = GetIt.instance<SubAccountCubit>();
    final currentAccount =
        subAccountCubit.state.derivativeSubAccount.firstOrNull;

    final defaultRequest = defaultFilter.toOrderBookRequest(
      accountId: currentAccount?.id ?? "",
    );

    emit(
      state.copyWith(
        regularOrderFilter: defaultFilter,
        conditionalOrderFilter: defaultFilter,
        regularOrderRequest: defaultRequest,
        conditionalOrderRequest: defaultRequest,
      ),
    );
  }

  /// Change current tab index
  void changeTab(int tabIndex) {
    emit(state.copyWith(currentTabIndex: tabIndex));
  }

  /// Update filter for Regular Orders tab
  void updateRegularOrderFilter(DerivativesFilterParam filterParam) {
    final subAccountCubit = GetIt.instance<SubAccountCubit>();
    final currentAccount =
        subAccountCubit.state.derivativeSubAccount.firstOrNull;

    final request = filterParam.toOrderBookRequest(
      accountId: currentAccount?.id ?? "",
    );

    emit(
      state.copyWith(
        regularOrderFilter: filterParam,
        regularOrderRequest: request,
      ),
    );
  }

  /// Update filter for Conditional Orders tab
  void updateConditionalOrderFilter(DerivativesFilterParam filterParam) {
    final subAccountCubit = GetIt.instance<SubAccountCubit>();
    final currentAccount =
        subAccountCubit.state.derivativeSubAccount.firstOrNull;

    final request = filterParam.toOrderBookRequest(
      accountId: currentAccount?.id ?? "",
    );

    emit(
      state.copyWith(
        conditionalOrderFilter: filterParam,
        conditionalOrderRequest: request,
      ),
    );
  }

  /// Update filter for current active tab
  void updateCurrentFilter(DerivativesFilterParam filterParam) {
    switch (state.currentTabIndex) {
      case 0:
        updateRegularOrderFilter(filterParam);
        break;
      case 1:
        updateConditionalOrderFilter(filterParam);
        break;
    }
  }

  /// Reset filter for current active tab to default
  void resetCurrentFilter() {
    const defaultFilter = DerivativesFilterParam(
      transactionType: OrderTypeEnum.all,
      orderStatus: [OrderStatusEnum.all],
    );
    updateCurrentFilter(defaultFilter);
  }

  /// Reset all filters to default
  void resetAllFilters() {
    const defaultFilter = DerivativesFilterParam(
      transactionType: OrderTypeEnum.all,
      orderStatus: [OrderStatusEnum.all],
    );

    final subAccountCubit = GetIt.instance<SubAccountCubit>();
    final currentAccount =
        subAccountCubit.state.derivativeSubAccount.firstOrNull;

    final defaultRequest = defaultFilter.toOrderBookRequest(
      accountId: currentAccount?.id ?? "",
    );

    emit(
      state.copyWith(
        regularOrderFilter: defaultFilter,
        conditionalOrderFilter: defaultFilter,
        regularOrderRequest: defaultRequest,
        conditionalOrderRequest: defaultRequest,
      ),
    );
  }
}

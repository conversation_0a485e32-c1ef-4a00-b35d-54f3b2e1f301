part of 'derivatives_order_book_filter_cubit.dart';

/// State for derivatives order book filter management
/// Follows dual cubit pattern for independent tab filter state
final class DerivativesOrderBookFilterState extends Equatable {
  const DerivativesOrderBookFilterState({
    this.currentTabIndex = 0,
    this.regularOrderFilter,
    this.conditionalOrderFilter,
    this.regularOrderRequest,
    this.conditionalOrderRequest,
  });

  /// Current active tab index (0 = Regular Orders, 1 = Conditional Orders)
  final int currentTabIndex;

  /// Filter parameters for Regular Orders tab
  final DerivativesFilterParam? regularOrderFilter;

  /// Filter parameters for Conditional Orders tab
  final DerivativesFilterParam? conditionalOrderFilter;

  /// OrderBookRequest for Regular Orders tab
  final OrderBookRequest? regularOrderRequest;

  /// OrderBookRequest for Conditional Orders tab
  final OrderBookRequest? conditionalOrderRequest;

  @override
  List<Object?> get props => [
    currentTabIndex,
    regularOrderFilter,
    conditionalOrder<PERSON>ilter,
    regularOrderRequest,
    conditionalOrderRequest,
  ];

  /// Get current filter based on active tab
  DerivativesFilterParam? get currentFilter {
    switch (currentTabIndex) {
      case 0:
        return regularOrderFilter;
      case 1:
        return conditionalOrderFilter;
      default:
        return regularOrderFilter;
    }
  }

  /// Get current OrderBookRequest based on active tab
  OrderBookRequest? get currentOrderRequest {
    switch (currentTabIndex) {
      case 0:
        return regularOrderRequest;
      case 1:
        return conditionalOrderRequest;
      default:
        return regularOrderRequest;
    }
  }

  /// Check if current filter is in default state
  bool get isCurrentFilterDefault {
    return currentFilter?.isDefault ?? true;
  }

  /// Check if any filter is applied (not default)
  bool get hasActiveFilter {
    return !isCurrentFilterDefault;
  }

  DerivativesOrderBookFilterState copyWith({
    int? currentTabIndex,
    DerivativesFilterParam? regularOrderFilter,
    DerivativesFilterParam? conditionalOrderFilter,
    OrderBookRequest? regularOrderRequest,
    OrderBookRequest? conditionalOrderRequest,
  }) {
    return DerivativesOrderBookFilterState(
      currentTabIndex: currentTabIndex ?? this.currentTabIndex,
      regularOrderFilter: regularOrderFilter ?? this.regularOrderFilter,
      conditionalOrderFilter:
          conditionalOrderFilter ?? this.conditionalOrderFilter,
      regularOrderRequest: regularOrderRequest ?? this.regularOrderRequest,
      conditionalOrderRequest:
          conditionalOrderRequest ?? this.conditionalOrderRequest,
    );
  }
}

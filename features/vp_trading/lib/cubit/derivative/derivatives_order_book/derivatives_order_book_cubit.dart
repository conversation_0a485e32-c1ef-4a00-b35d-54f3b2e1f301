import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:vp_common/error/show_error.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/repository/command_history_repository.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/model/request/order/order_book_request.dart';

part 'derivatives_order_book_state.dart';

class DerivativesOrderBookCubit extends Cubit<DerivativesOrderBookState> {
  DerivativesOrderBookCubit() : super(const DerivativesOrderBookState());

  final CommandHistoryRepository _commandHistoryRepository =
      GetIt.instance<CommandHistoryRepository>();

  void init() {
    print("1111111111111111111111111111111111111111111111");
    emit(
      state.copyWith(
        request: state.request.copyWith(
          accountId:
              GetIt.instance<SubAccountCubit>()
                  .state
                  .derivativeSubAccount
                  .firstOrNull
                  ?.id ??
              "",
        ),
      ),
    );
    loadData();
  }

  Future<void> loadData() async {
    print("222222222222222222222222222222222222222222222222");
    try {
      emit(state.copyWith(isLoading: true));
      final result = await _commandHistoryRepository.getOrder(
        queries: state.request,
      );

      final items = result.data?.content ?? [];
      emit(state.copyWith(isLoading: false, listItems: items));
    } catch (e) {
      showError(e);
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: await getErrorMessage(e),
        ),
      );
      debugPrintStack(stackTrace: StackTrace.current);
    }
  }

  void applyFilter(OrderBookRequest filterRequest) {
    emit(state.copyWith(request: filterRequest));
    loadData();
  }

  void resetErrorMessage() {
    emit(state.copyWith(errorMessage: null));
  }
}

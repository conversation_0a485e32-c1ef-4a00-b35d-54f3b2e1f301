part of 'derivatives_order_book_cubit.dart';

final class DerivativesOrderBookState extends Equatable {
  final bool isLoading;
  final List<OrderBookModel> listItems;
  final OrderBookRequest request;
  final String? errorMessage;

  const DerivativesOrderBookState({
    this.isLoading = false,
    this.errorMessage,
    this.listItems = const [],
    this.request = const OrderBookRequest(),
  });

  @override
  List<Object?> get props => [isLoading, errorMessage, listItems, request];

  DerivativesOrderBookState copyWith({
    bool? isLoading,
    String? errorMessage,
    List<OrderBookModel>? listItems,
    OrderBookRequest? request,
  }) {
    return DerivativesOrderBookState(
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      listItems: listItems ?? this.listItems,
      request: request ?? this.request,
    );
  }
}

import 'package:equatable/equatable.dart';
import 'package:vp_trading/model/request/order/order_book_request.dart';
import 'package:vp_trading/screen/order_container/enum/order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';

/// Filter parameters for derivatives order book
class DerivativesFilterParam extends Equatable {
  final OrderTypeEnum? transactionType;
  final List<OrderStatusEnum>? orderStatus;

  const DerivativesFilterParam({this.transactionType, this.orderStatus});

  @override
  List<Object?> get props => [transactionType, orderStatus];

  DerivativesFilterParam copyWith({
    OrderTypeEnum? transactionType,
    List<OrderStatusEnum>? orderStatus,
  }) {
    return DerivativesFilterParam(
      transactionType: transactionType ?? this.transactionType,
      orderStatus: orderStatus ?? this.orderStatus,
    );
  }

  /// Check if filter is in default state (All selected for both criteria)
  bool get isDefault {
    final isTransactionTypeDefault =
        transactionType == null || transactionType == OrderTypeEnum.all;
    final isOrderStatusDefault =
        orderStatus == null ||
        orderStatus!.isEmpty ||
        orderStatus!.contains(OrderStatusEnum.all);

    return isTransactionTypeDefault && isOrderStatusDefault;
  }

  /// Get the combined status codes for API request
  String get statusCodesForRequest {
    if (orderStatus == null ||
        orderStatus!.isEmpty ||
        orderStatus!.contains(OrderStatusEnum.all)) {
      return "";
    }

    final allCodes = <String>[];
    for (final status in orderStatus!) {
      if (status != OrderStatusEnum.all) {
        allCodes.addAll(status.codes);
      }
    }

    return allCodes.join(',');
  }

  /// Get transaction type code for API request
  String get transactionTypeCodeForRequest {
    if (transactionType == null || transactionType == OrderTypeEnum.all) {
      return "";
    }
    return transactionType!.codeRequest;
  }

  /// Convert filter parameters to OrderBookRequest
  OrderBookRequest toOrderBookRequest({
    String? accountId,
  }) {
    return OrderBookRequest(
      accountId: accountId,
      orderStatus: statusCodesForRequest.isEmpty ? null : statusCodesForRequest,
      side:
          transactionTypeCodeForRequest.isEmpty
              ? null
              : transactionTypeCodeForRequest,
    );
  }
}

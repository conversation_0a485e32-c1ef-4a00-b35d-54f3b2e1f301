import 'package:alice_dio/alice_dio_adapter.dart';
import 'package:curl_logger_dio_interceptor/curl_logger_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/interceptor/error_handerling_interceptor.dart';
import 'package:vp_core/interceptor/session_interceptor.dart';

// RestClient getRestClient(
//   String path, {
//   List<Interceptor> interceptors = const [],
//   bool sendLogWhenError = true,
//   bool retryWhenFail = true,
//   bool handleUnauthorizedError = true,
// }) {
//   return RestClient(
//     path,
//     interceptors: [
//       ...interceptors,
//       SessionInterceptor(),
//       if (handleUnauthorizedError) HandleUnauthorizedInterceptor(),
//       if (retryWhenFail) RetryConnectionChangeInterceptor(),
//       IAMCentralizeInterceptor(),
//       FSSCentralizeInterceptor(),
//       if (sendLogWhenError) LoggingInterceptor(),
//       LogAPIInterceptor(),
//       DerivativeInterceptor(),
//     ],
//   );
// }

Future<Dio> setupDio(String baseUrl) async {
  const timeout = Duration(seconds: 30);
  final options = BaseOptions(
    connectTimeout: timeout,
    receiveTimeout: timeout,
    // validateStatus: (status) {
    //   return true;
    // },
    baseUrl: baseUrl,
  );
  final dio = Dio(options);

  /// Adapt data (according to your own data structure, you can choose to add it)
  // dio.interceptors.add(TokenInterceptor());

  /// Print Log (production mode removal)
  // if (F.  != Flavor.prod) {
  dio.options.headers['content-Type'] = 'application/json';
  dio.interceptors.add(SessionInterceptor());

  dio.interceptors.add(CurlLoggerDioInterceptor(printOnSuccess: true));
  dio.interceptors.add(LogInterceptor(requestBody: true, responseBody: true));
  dio.interceptors.add(LogAPIInterceptor());
  dio.interceptors.add(GetIt.instance<AliceDioAdapter>());
  dio.interceptors.add(ErrorHanderlingInterceptor());
  // // }
  // final info = await PackageInfo.fromPlatform();
  // dio.interceptors.add(AppInfoInterceptor(info));
  return dio;
}
